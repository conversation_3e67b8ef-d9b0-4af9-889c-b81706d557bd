"""
# 基础正弦波处理模块

模块路径：`sweeper400.analyze.basic_sine`

本模块包含与最简单的**单频正弦波**相关的类和函数。
"""

import numpy as np
from typing import Optional
from sweeper400.analyze.my_dtypes import (
    PositiveInt,
    PositiveFloat,
    SamplingInfo,
    Waveform,
)
from sweeper400.logger import get_logger

# 获取模块日志器
logger = get_logger(__name__)


def init_sampling_info(
    sampling_rate: PositiveInt, samples_num: PositiveInt
) -> SamplingInfo:
    """
    标准化地生成采样信息字典

    Args:
        sampling_rate: 采样率，必须为正整数（Hz）
        samples_num: 总采样数，必须为正整数

    Returns:
        sampling_info: 包含采样率和采样数信息的字典

    Examples:
        ```python
        >>> sampling_info = init_sampling_info(PositiveInt(1000), PositiveInt(2048))
        >>> print(sampling_info)
        {'sampling_rate': 1000, 'samples_num': 2048}
        ```
    """
    logger.debug(
        f"创建采样信息: sampling_rate={sampling_rate}Hz, samples_num={samples_num}"
    )

    sampling_info: SamplingInfo = {
        "sampling_rate": sampling_rate,
        "samples_num": samples_num,
    }

    return sampling_info


def sine_wave_vvi(
    sampling_info: SamplingInfo,
    frequency: PositiveFloat,
    amplitude: PositiveFloat = 1.0,
    phase: float = 0.0,
    timestamp: Optional[np.datetime64] = None,
) -> Waveform:
    """
    使用几个简单的参数生成包含单频正弦波时域信号的Waveform对象

    Args:
        sampling_info: 采样信息，包含采样率和采样点数
        frequency: 正弦波频率（Hz）
        amplitude: 正弦波幅值（无单位），默认值为1.0
        phase: 波形的弧度制初始相位（rad），默认值为0.0
        timestamp: 采样开始时间戳，默认值为None

    Returns:
        output_sine_wave: 包含单频正弦波的Waveform对象

    Examples:
        ```python
        >>> sampling_info = {"sampling_rate": 1000.0, "samples_num": 1024}
        >>> sine_wave = sine_wave_vvi(sampling_info, frequency=50.0)
        >>> print(sine_wave.shape)
        (1024,)
        >>> print(sine_wave.sampling_rate)
        1000.0
        ```
    """
    logger.debug(
        f"生成正弦波: frequency={frequency}Hz, amplitude={amplitude}, "
        f"phase={phase}rad, sampling_rate={sampling_info['sampling_rate']}Hz, "
        f"samples_num={sampling_info['samples_num']}, timestamp={timestamp}"
    )

    # 从采样信息中提取参数
    sampling_rate = sampling_info["sampling_rate"]
    samples_num = sampling_info["samples_num"]

    # 生成时间序列
    # 使用 linspace 生成从 0 到 (samples_num-1)/sampling_rate 的时间点
    # endpoint=False 确保不包含最后一个时间点，避免周期性信号的重复
    duration = samples_num / sampling_rate
    time_array = np.linspace(0, duration, samples_num, endpoint=False)

    # 生成正弦波数据
    # y(t) = amplitude * sin(2π * frequency * t + phase)
    sine_data = amplitude * np.sin(2 * np.pi * frequency * time_array + phase)

    # 创建Waveform对象
    output_sine_wave = Waveform(
        input_array=sine_data,
        sampling_rate=sampling_rate,
        timestamp=timestamp,
    )

    logger.debug(f"成功生成正弦波Waveform对象: {output_sine_wave}")

    return output_sine_wave


class sine_waveform_vvi:
    """
    连续正弦波形生成器类

    使用几个简单的参数多次生成**连续**的单频正弦Waveform对象。
    初相位会智能更新，因此多次生成的waveform首尾相接可合成一个无缝的连续波形。

    Attributes:
        next_phase: 下一次合成Waveform的弧度制初相位（单位rad）
        next_timestamp: 下一次合成Waveform的时间戳

    Examples:
        ```python
        >>> sampling_info = {"sampling_rate": 1000.0, "samples_num": 1024}
        >>> generator = sine_waveform_vvi()
        >>> wave1 = generator.generate(sampling_info, frequency=50.0)
        >>> wave2 = generator.generate(sampling_info, frequency=50.0)
        # wave1和wave2可以无缝连接
        ```
    """

    def __init__(
        self,
        next_phase: float = 0.0,
        next_timestamp: Optional[np.datetime64] = None,
    ) -> None:
        """
        初始化连续正弦波形生成器

        Args:
            next_phase: 下一次合成Waveform的弧度制初相位（单位rad），默认为0.0
            next_timestamp: 下一次合成Waveform的时间戳，默认为None
        """
        self.next_phase = next_phase
        self.next_timestamp = next_timestamp

        logger.debug(
            f"初始化sine_waveform_vvi生成器: "
            f"next_phase={self.next_phase}rad, next_timestamp={self.next_timestamp}"
        )

    def generate(
        self,
        sampling_info: SamplingInfo,
        frequency: PositiveFloat,
        amplitude: PositiveFloat = 1.0,
    ) -> Waveform:
        """
        生成连续的正弦波形

        Args:
            sampling_info: 采样信息，包含采样率和采样点数
            frequency: 正弦波频率（Hz）
            amplitude: 正弦波幅值（无单位），默认值为1.0

        Returns:
            output_sine_wave: 包含单频正弦波的Waveform对象
        """
        logger.debug(
            f"生成连续正弦波: frequency={frequency}Hz, amplitude={amplitude}, "
            f"使用phase={self.next_phase}rad, timestamp={self.next_timestamp}"
        )

        # 调用sine_wave_vvi函数生成波形
        output_sine_wave = sine_wave_vvi(
            sampling_info=sampling_info,
            frequency=frequency,
            amplitude=amplitude,
            phase=self.next_phase,
            timestamp=self.next_timestamp,
        )

        # 更新下一次生成的相位和时间戳
        self._update_next_parameters(output_sine_wave, frequency)

        logger.debug(
            f"成功生成连续正弦波，更新参数: "
            f"next_phase={self.next_phase}rad, next_timestamp={self.next_timestamp}"
        )

        return output_sine_wave

    def _update_next_parameters(
        self, current_waveform: Waveform, frequency: PositiveFloat
    ) -> None:
        """
        更新下一次生成使用的相位和时间戳

        Args:
            current_waveform: 当前生成的波形
            frequency: 当前波形的频率（Hz）
        """
        # 计算下一次的相位，确保波形连续
        # 相位增量 = 2π * frequency * duration
        phase_increment = 2 * np.pi * frequency * current_waveform.duration
        self.next_phase = (self.next_phase + phase_increment) % (2 * np.pi)

        # 更新下一次的时间戳
        if current_waveform.timestamp is not None:
            # 计算时长对应的纳秒数
            duration_ns = int(current_waveform.duration * 1e9)
            # 使用numpy的timedelta64进行时间戳计算，保持纳秒精度
            self.next_timestamp = current_waveform.timestamp + np.timedelta64(
                duration_ns, "ns"
            )
        else:
            self.next_timestamp = None
