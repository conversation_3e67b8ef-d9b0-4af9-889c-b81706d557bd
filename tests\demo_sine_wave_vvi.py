"""
sine_wave_vvi 函数的演示脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from sweeper400.analyze import sine_wave_vvi, SamplingInfo


def demo_basic_sine_wave():
    """演示基本正弦波生成"""
    print("=== 基本正弦波生成演示 ===")
    
    # 创建采样信息
    sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,  # 1kHz采样率
        "samples_num": 1000       # 1000个采样点，正好1秒
    }
    
    # 生成50Hz正弦波
    sine_wave = sine_wave_vvi(sampling_info, frequency=50.0)
    
    print(f"生成的波形信息:")
    print(f"  形状: {sine_wave.shape}")
    print(f"  采样率: {sine_wave.sampling_rate} Hz")
    print(f"  持续时间: {sine_wave.duration:.3f} 秒")
    print(f"  通道数: {sine_wave.channels_num}")
    print(f"  采样点数: {sine_wave.samples_num}")
    print(f"  时间戳: {sine_wave.timestamp}")
    print(f"  最大值: {np.max(sine_wave):.6f}")
    print(f"  最小值: {np.min(sine_wave):.6f}")
    print()


def demo_different_parameters():
    """演示不同参数的正弦波"""
    print("=== 不同参数正弦波演示 ===")
    
    sampling_info: SamplingInfo = {
        "sampling_rate": 2000.0,
        "samples_num": 2000
    }
    
    # 生成不同参数的正弦波
    waves = {
        "基础波形 (10Hz)": sine_wave_vvi(sampling_info, 10.0),
        "高幅值波形 (10Hz, 幅值=3)": sine_wave_vvi(sampling_info, 10.0, amplitude=3.0),
        "相位偏移波形 (10Hz, 相位=π/4)": sine_wave_vvi(sampling_info, 10.0, phase=np.pi/4),
        "高频波形 (100Hz)": sine_wave_vvi(sampling_info, 100.0),
    }
    
    for name, wave in waves.items():
        print(f"{name}:")
        print(f"  最大值: {np.max(wave):.3f}, 最小值: {np.min(wave):.3f}")
        print(f"  RMS值: {np.sqrt(np.mean(wave**2)):.3f}")
    print()


def demo_visualization():
    """演示波形可视化"""
    print("=== 波形可视化演示 ===")
    
    sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,
        "samples_num": 500  # 0.5秒的数据
    }
    
    # 生成几个不同的波形
    wave1 = sine_wave_vvi(sampling_info, 5.0, amplitude=1.0)  # 5Hz
    wave2 = sine_wave_vvi(sampling_info, 10.0, amplitude=0.5)  # 10Hz, 小幅值
    wave3 = sine_wave_vvi(sampling_info, 5.0, amplitude=1.0, phase=np.pi/2)  # 5Hz, 90度相位
    
    # 创建时间轴
    time_axis = np.linspace(0, wave1.duration, wave1.samples_num)
    
    # 绘图
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(time_axis, wave1)
    plt.title('5Hz 正弦波 (幅值=1)')
    plt.xlabel('时间 (秒)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(time_axis, wave2)
    plt.title('10Hz 正弦波 (幅值=0.5)')
    plt.xlabel('时间 (秒)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(time_axis, wave1, label='原始波形')
    plt.plot(time_axis, wave3, label='90°相位偏移')
    plt.title('相位对比 (5Hz)')
    plt.xlabel('时间 (秒)')
    plt.ylabel('幅值')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    plt.plot(time_axis, wave1 + wave2, label='5Hz + 10Hz')
    plt.plot(time_axis, wave1, '--', alpha=0.7, label='5Hz')
    plt.plot(time_axis, wave2, '--', alpha=0.7, label='10Hz')
    plt.title('波形叠加')
    plt.xlabel('时间 (秒)')
    plt.ylabel('幅值')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("波形图已显示")


if __name__ == "__main__":
    demo_basic_sine_wave()
    demo_different_parameters()
    
    # 询问是否显示图形
    try:
        show_plots = input("是否显示波形图？(y/n): ").lower().strip()
        if show_plots in ['y', 'yes', '是']:
            demo_visualization()
    except KeyboardInterrupt:
        print("\n演示结束")
