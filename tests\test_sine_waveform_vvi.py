"""
测试 sine_waveform_vvi 类的功能
"""

import numpy as np
import pytest
from sweeper400.analyze import sine_waveform_vvi, SamplingInfo


class TestSineWaveformVvi:
    """测试 sine_waveform_vvi 类"""
    
    def test_init_default_parameters(self):
        """测试默认参数初始化"""
        generator = sine_waveform_vvi()
        assert generator.next_phase == 0.0
        assert generator.next_timestamp is None
    
    def test_init_custom_parameters(self):
        """测试自定义参数初始化"""
        custom_phase = np.pi / 4
        custom_timestamp = np.datetime64("2024-01-01T12:00:00", "ns")
        
        generator = sine_waveform_vvi(
            next_phase=custom_phase,
            next_timestamp=custom_timestamp
        )
        
        assert generator.next_phase == custom_phase
        assert generator.next_timestamp == custom_timestamp
    
    def test_single_generation(self):
        """测试单次波形生成"""
        generator = sine_waveform_vvi()
        
        sampling_info: SamplingInfo = {
            "sampling_rate": 1000.0,
            "samples_num": 1000
        }
        
        waveform = generator.generate(
            sampling_info=sampling_info,
            frequency=50.0,
            amplitude=2.0
        )
        
        # 验证波形基本属性
        assert waveform.shape == (1000,)
        assert waveform.sampling_rate == 1000.0
        assert waveform.duration == 1.0  # 1000 samples / 1000 Hz = 1 second
        assert np.isclose(np.max(waveform), 2.0, atol=1e-10)
        assert np.isclose(np.min(waveform), -2.0, atol=1e-10)
    
    def test_continuous_generation_phase(self):
        """测试连续生成时相位的连续性"""
        generator = sine_waveform_vvi()
        
        sampling_info: SamplingInfo = {
            "sampling_rate": 1000.0,
            "samples_num": 500  # 0.5秒
        }
        
        frequency = 50.0  # 50Hz
        
        # 生成第一个波形
        wave1 = generator.generate(sampling_info, frequency=frequency)
        
        # 记录第一个波形的最后一个值
        last_value_wave1 = wave1[-1]
        
        # 生成第二个波形
        wave2 = generator.generate(sampling_info, frequency=frequency)
        
        # 记录第二个波形的第一个值
        first_value_wave2 = wave2[0]
        
        # 验证连续性：第二个波形的第一个值应该与第一个波形的最后一个值连续
        # 计算期望的下一个值
        dt = 1.0 / sampling_info["sampling_rate"]  # 时间步长
        expected_next_value = 2.0 * np.sin(2 * np.pi * frequency * wave1.duration)
        
        # 由于数值精度问题，使用较小的容差
        assert np.isclose(first_value_wave2, expected_next_value, atol=1e-10)
    
    def test_continuous_generation_timestamp(self):
        """测试连续生成时时间戳的连续性"""
        initial_timestamp = np.datetime64("2024-01-01T12:00:00", "ns")
        generator = sine_waveform_vvi(next_timestamp=initial_timestamp)
        
        sampling_info: SamplingInfo = {
            "sampling_rate": 1000.0,
            "samples_num": 1000  # 1秒
        }
        
        # 生成第一个波形
        wave1 = generator.generate(sampling_info, frequency=50.0)
        assert wave1.timestamp == initial_timestamp
        
        # 生成第二个波形
        wave2 = generator.generate(sampling_info, frequency=50.0)
        
        # 验证时间戳连续性
        expected_timestamp = initial_timestamp + np.timedelta64(int(1e9), "ns")  # 1秒后
        assert wave2.timestamp == expected_timestamp
    
    def test_phase_wrapping(self):
        """测试相位的正确包装（0到2π）"""
        generator = sine_waveform_vvi(next_phase=1.5 * np.pi)
        
        sampling_info: SamplingInfo = {
            "sampling_rate": 1000.0,
            "samples_num": 1000  # 1秒
        }
        
        # 生成一个波形，这会导致相位增加
        generator.generate(sampling_info, frequency=50.0)  # 50Hz * 1s = 50个周期
        
        # 验证相位被正确包装在[0, 2π)范围内
        assert 0 <= generator.next_phase < 2 * np.pi
    
    def test_different_frequencies(self):
        """测试不同频率下的连续性"""
        generator = sine_waveform_vvi()
        
        sampling_info: SamplingInfo = {
            "sampling_rate": 1000.0,
            "samples_num": 500  # 0.5秒
        }
        
        # 生成不同频率的波形
        wave1 = generator.generate(sampling_info, frequency=25.0)
        wave2 = generator.generate(sampling_info, frequency=100.0)
        
        # 验证两个波形都生成成功
        assert wave1.shape == (500,)
        assert wave2.shape == (500,)
        assert wave1.sampling_rate == 1000.0
        assert wave2.sampling_rate == 1000.0


if __name__ == "__main__":
    # 运行测试
    test_instance = TestSineWaveformVvi()
    
    print("运行 sine_waveform_vvi 类测试...")
    
    test_instance.test_init_default_parameters()
    print("✓ 默认参数初始化测试通过")
    
    test_instance.test_init_custom_parameters()
    print("✓ 自定义参数初始化测试通过")
    
    test_instance.test_single_generation()
    print("✓ 单次波形生成测试通过")
    
    test_instance.test_continuous_generation_phase()
    print("✓ 连续生成相位连续性测试通过")
    
    test_instance.test_continuous_generation_timestamp()
    print("✓ 连续生成时间戳连续性测试通过")
    
    test_instance.test_phase_wrapping()
    print("✓ 相位包装测试通过")
    
    test_instance.test_different_frequencies()
    print("✓ 不同频率测试通过")
    
    print("\n所有测试通过！sine_waveform_vvi 类工作正常。")
