"""
sine_wave_vvi 函数的简单演示脚本（非交互式）
"""

import numpy as np
from sweeper400.analyze import sine_wave_vvi, SamplingInfo


def main():
    """主演示函数"""
    print("=== sine_wave_vvi 函数演示 ===\n")
    
    # 1. 基本使用
    print("1. 基本使用演示:")
    sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,  # 1kHz采样率
        "samples_num": 1000       # 1000个采样点
    }
    
    sine_wave = sine_wave_vvi(sampling_info, frequency=50.0)
    print(f"   生成50Hz正弦波: shape={sine_wave.shape}, 采样率={sine_wave.sampling_rate}Hz")
    print(f"   持续时间={sine_wave.duration:.3f}秒, 最大值={np.max(sine_wave):.3f}")
    
    # 2. 指定幅值
    print("\n2. 指定幅值演示:")
    sine_wave_amp = sine_wave_vvi(sampling_info, frequency=50.0, amplitude=2.5)
    print(f"   生成幅值为2.5的50Hz正弦波: 最大值={np.max(sine_wave_amp):.3f}")
    
    # 3. 指定相位
    print("\n3. 指定相位演示:")
    sine_wave_phase = sine_wave_vvi(sampling_info, frequency=50.0, phase=np.pi/2)
    print(f"   生成相位为π/2的50Hz正弦波: 第一个点值={sine_wave_phase[0]:.3f} (应接近1)")
    
    # 4. 验证频率准确性
    print("\n4. 频率准确性验证:")
    test_sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,
        "samples_num": 1000  # 正好1秒
    }
    test_freq = 10.0  # 10Hz
    test_wave = sine_wave_vvi(test_sampling_info, frequency=test_freq)
    
    # 计算过零点数量来验证频率
    zero_crossings = np.sum(np.diff(np.sign(test_wave)) != 0)
    expected_crossings = test_freq * 2  # 每个周期有2个过零点
    print(f"   10Hz正弦波过零点数: {zero_crossings} (期望: {expected_crossings})")
    
    # 5. 不同采样率测试
    print("\n5. 不同采样率测试:")
    high_rate_info: SamplingInfo = {
        "sampling_rate": 10000.0,  # 10kHz
        "samples_num": 5000        # 0.5秒
    }
    high_rate_wave = sine_wave_vvi(high_rate_info, frequency=100.0)
    print(f"   高采样率波形: 采样率={high_rate_wave.sampling_rate}Hz, 持续时间={high_rate_wave.duration:.3f}秒")
    
    # 6. 波形统计信息
    print("\n6. 波形统计信息:")
    stats_wave = sine_wave_vvi(sampling_info, frequency=25.0, amplitude=1.5)
    rms_value = np.sqrt(np.mean(stats_wave**2))
    theoretical_rms = 1.5 / np.sqrt(2)  # 理论RMS值
    print(f"   25Hz, 幅值1.5的正弦波:")
    print(f"   - 实际RMS值: {rms_value:.6f}")
    print(f"   - 理论RMS值: {theoretical_rms:.6f}")
    print(f"   - 误差: {abs(rms_value - theoretical_rms):.6f}")
    
    print("\n=== 演示完成 ===")


if __name__ == "__main__":
    main()
