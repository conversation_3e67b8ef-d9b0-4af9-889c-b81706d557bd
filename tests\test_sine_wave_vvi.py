"""
测试 sine_wave_vvi 函数的功能
"""

import numpy as np
import pytest
from sweeper400.analyze import sine_wave_vvi, SamplingInfo


def test_sine_wave_vvi_basic():
    """测试基本的正弦波生成功能"""
    # 准备测试数据
    sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,
        "samples_num": 1024
    }
    frequency = 50.0
    
    # 调用函数
    result = sine_wave_vvi(sampling_info, frequency)
    
    # 验证结果
    assert result.shape == (1024,)
    assert result.sampling_rate == 1000.0
    assert result.samples_num == 1024
    assert result.channels_num == 1
    assert abs(result.duration - 1.024) < 1e-10  # 1024/1000 = 1.024秒
    
    # 验证正弦波的基本特性
    # 检查最大值和最小值接近1和-1（默认幅值为1）
    assert abs(np.max(result) - 1.0) < 0.01
    assert abs(np.min(result) + 1.0) < 0.01


def test_sine_wave_vvi_with_amplitude():
    """测试指定幅值的正弦波生成"""
    sampling_info: SamplingInfo = {
        "sampling_rate": 2000.0,
        "samples_num": 512
    }
    frequency = 100.0
    amplitude = 2.5
    
    result = sine_wave_vvi(sampling_info, frequency, amplitude=amplitude)
    
    # 验证幅值
    assert abs(np.max(result) - amplitude) < 0.01
    assert abs(np.min(result) + amplitude) < 0.01


def test_sine_wave_vvi_with_phase():
    """测试指定相位的正弦波生成"""
    sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,
        "samples_num": 1000
    }
    frequency = 10.0
    phase = np.pi / 2  # 90度相位
    
    result_no_phase = sine_wave_vvi(sampling_info, frequency)
    result_with_phase = sine_wave_vvi(sampling_info, frequency, phase=phase)
    
    # 90度相位差应该使正弦波变成余弦波
    # sin(x + π/2) = cos(x)
    # 验证第一个点的值
    assert abs(result_with_phase[0] - 1.0) < 0.01  # cos(0) = 1
    assert abs(result_no_phase[0] - 0.0) < 0.01    # sin(0) = 0


def test_sine_wave_vvi_frequency_accuracy():
    """测试频率准确性"""
    sampling_info: SamplingInfo = {
        "sampling_rate": 1000.0,
        "samples_num": 1000  # 正好1秒的数据
    }
    frequency = 5.0  # 5Hz，在1秒内应该有5个完整周期
    
    result = sine_wave_vvi(sampling_info, frequency)
    
    # 使用FFT验证频率
    fft_result = np.fft.fft(result)
    freqs = np.fft.fftfreq(len(result), 1/sampling_info["sampling_rate"])
    
    # 找到最大幅值对应的频率
    max_idx = np.argmax(np.abs(fft_result[1:len(result)//2])) + 1  # 排除直流分量
    dominant_freq = abs(freqs[max_idx])
    
    # 验证主要频率分量接近设定频率
    assert abs(dominant_freq - frequency) < 0.1


if __name__ == "__main__":
    # 运行基本测试
    test_sine_wave_vvi_basic()
    test_sine_wave_vvi_with_amplitude()
    test_sine_wave_vvi_with_phase()
    test_sine_wave_vvi_frequency_accuracy()
    print("所有测试通过！")
